@tailwind base;
@tailwind components;
@tailwind utilities;

/* Hero Section Animations */
@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }
  33% {
    transform: translate(30px, -50px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

.animate-blob {
  animation: blob 7s infinite;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}

/* ==========================================================================
   CSS CUSTOM PROPERTIES - DYNAMIC COLOR SYSTEM
   ========================================================================== */

/* 
   RENK DEĞİŞTİRME REHBERİ:
   
   Tüm site renklerini değiştirmek için sadece aşağıdaki CSS değişkenlerini güncelleyin:
   
   ÖRNEKLERİ:
   
   YEŞİL TEMA İÇİN:
   --color-primary-600: #059669; (emerald-600)
   --color-primary-700: #047857; (emerald-700)
   --color-primary-800: #065f46; (emerald-800)
   
   KIRMIZI TEMA İÇİN:
   --color-primary-600: #dc2626; (red-600)
   --color-primary-700: #b91c1c; (red-700)
   --color-primary-800: #991b1b; (red-800)
   
   MOR TEMA İÇİN:
   --color-primary-600: #9333ea; (purple-600)
   --color-primary-700: #7c3aed; (purple-700)
   --color-primary-800: #6b21a8; (purple-800)
*/

:root {
  /* Primary Brand Colors - Change these to update entire theme */
  --color-primary-50: #eff6ff;
  --color-primary-100: #dbeafe;
  --color-primary-200: #bfdbfe;
  --color-primary-300: #93c5fd;
  --color-primary-400: #60a5fa;
  --color-primary-500: #3b82f6;
  --color-primary-600: #2563eb;
  --color-primary-700: #1d4ed8;
  --color-primary-800: #1e40af;
  --color-primary-900: #1e3a8a;
  
  /* Secondary Colors */
  --color-secondary-50: #f8fafc;
  --color-secondary-100: #f1f5f9;
  --color-secondary-200: #e2e8f0;
  --color-secondary-300: #cbd5e1;
  --color-secondary-400: #94a3b8;
  --color-secondary-500: #64748b;
  --color-secondary-600: #475569;
  --color-secondary-700: #334155;
  --color-secondary-800: #1e293b;
  --color-secondary-900: #0f172a;
  
  /* Success Colors */
  --color-success-50: #f0fdf4;
  --color-success-100: #dcfce7;
  --color-success-500: #22c55e;
  --color-success-600: #16a34a;
  
  /* Warning Colors */
  --color-warning-50: #fffbeb;
  --color-warning-100: #fef3c7;
  --color-warning-500: #f59e0b;
  --color-warning-600: #d97706;
  
  /* Danger Colors */
  --color-danger-50: #fef2f2;
  --color-danger-100: #fee2e2;
  --color-danger-500: #ef4444;
  --color-danger-600: #dc2626;
}

/* ==========================================================================
   BASE STYLES - Global Overrides
   ========================================================================== */

@layer base {
  /* Smooth scrolling and better font rendering */
  html {
    scroll-behavior: smooth;
  }
  
  body {
    @apply font-sans text-secondary-900 antialiased;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  
  /* Typography improvements */
  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold text-secondary-900;
    letter-spacing: -0.025em;
  }
  
  h1 {
    @apply text-3xl lg:text-4xl xl:text-5xl;
  }
  
  h2 {
    @apply text-2xl lg:text-3xl xl:text-4xl;
  }
  
  h3 {
    @apply text-xl lg:text-2xl;
  }
  
  h4 {
    @apply text-lg lg:text-xl;
  }
  
  /* Link transitions */
  a {
    @apply transition-colors duration-200;
  }
  
  /* Focus styles for accessibility */
  *:focus-visible {
    @apply outline-none;
  }
  
  /* Selection color */
  ::selection {
    @apply bg-primary-100 text-primary-900;
  }
}

/* ==========================================================================
   COMPONENT STYLES - Reusable Components
   ========================================================================== */

@layer components {
  
  /* Dynamic Color Classes */
  .bg-primary-50 { background-color: var(--color-primary-50); }
  .bg-primary-100 { background-color: var(--color-primary-100); }
  .bg-primary-200 { background-color: var(--color-primary-200); }
  .bg-primary-300 { background-color: var(--color-primary-300); }
  .bg-primary-400 { background-color: var(--color-primary-400); }
  .bg-primary-500 { background-color: var(--color-primary-500); }
  .bg-primary-600 { background-color: var(--color-primary-600); }
  .bg-primary-700 { background-color: var(--color-primary-700); }
  .bg-primary-800 { background-color: var(--color-primary-800); }
  .bg-primary-900 { background-color: var(--color-primary-900); }
  
  .text-primary-50 { color: var(--color-primary-50); }
  .text-primary-100 { color: var(--color-primary-100); }
  .text-primary-200 { color: var(--color-primary-200); }
  .text-primary-300 { color: var(--color-primary-300); }
  .text-primary-400 { color: var(--color-primary-400); }
  .text-primary-500 { color: var(--color-primary-500); }
  .text-primary-600 { color: var(--color-primary-600); }
  .text-primary-700 { color: var(--color-primary-700); }
  .text-primary-800 { color: var(--color-primary-800); }
  .text-primary-900 { color: var(--color-primary-900); }
  
  .border-primary-50 { border-color: var(--color-primary-50); }
  .border-primary-100 { border-color: var(--color-primary-100); }
  .border-primary-200 { border-color: var(--color-primary-200); }
  .border-primary-300 { border-color: var(--color-primary-300); }
  .border-primary-400 { border-color: var(--color-primary-400); }
  .border-primary-500 { border-color: var(--color-primary-500); }
  .border-primary-600 { border-color: var(--color-primary-600); }
  .border-primary-700 { border-color: var(--color-primary-700); }
  .border-primary-800 { border-color: var(--color-primary-800); }
  .border-primary-900 { border-color: var(--color-primary-900); }

  /* Button System */
  .btn-primary {
    background-color: var(--color-primary-600);
    color: white;
    @apply font-medium py-2 px-4 rounded-lg;
    @apply transition-all duration-200 ease-in-out;
    @apply focus:outline-none;
    @apply disabled:opacity-50 disabled:cursor-not-allowed;
    @apply shadow-sm hover:shadow-md;
  }
  
  .btn-primary:hover {
    background-color: var(--color-primary-700);
  }
  
  .btn-primary:active {
    background-color: var(--color-primary-800);
  }
  
  .btn-secondary {
    background-color: var(--color-secondary-100);
    color: var(--color-secondary-700);
    @apply font-medium py-2 px-4 rounded-lg;
    @apply transition-all duration-200 ease-in-out;
    @apply focus:outline-none;
  }
  
  .btn-secondary:hover {
    background-color: var(--color-secondary-200);
    color: var(--color-secondary-800);
  }
  
  .btn-secondary:active {
    background-color: var(--color-secondary-300);
  }
  
  .btn-outline {
    border: 1px solid var(--color-primary-600);
    color: var(--color-primary-600);
    background-color: transparent;
    @apply font-medium py-2 px-4 rounded-lg;
    @apply transition-all duration-200 ease-in-out;
    @apply focus:outline-none;
  }
  
  .btn-outline:hover {
    background-color: var(--color-primary-600);
    color: white;
    border-color: var(--color-primary-600);
  }
  
  .btn-outline:active {
    background-color: var(--color-primary-700);
    border-color: var(--color-primary-700);
  }
  
  /* Card System */
  .card {
    @apply bg-white rounded-xl shadow-sm border border-gray-100 p-6;
  }
  
  .card-hover {
    @apply card hover:shadow-lg hover:border-gray-200;
    @apply transition-all duration-300 ease-in-out;
    @apply cursor-pointer;
  }
  
  .card-hover:hover {
    @apply -translate-y-1;
  }
  
  /* Form Elements */
  .input-field {
    @apply w-full px-3 py-2 border border-gray-300 rounded-lg;
    @apply focus:outline-none;
    @apply placeholder-gray-400;
    @apply disabled:bg-gray-50 disabled:text-gray-500 disabled:cursor-not-allowed;
  }
  
  .input-field:hover:not(:disabled) {
    @apply border-gray-400;
  }
  
  /* Badge System */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .badge-success {
    @apply badge bg-green-100 text-green-800;
  }
  
  .badge-warning {
    @apply badge bg-yellow-100 text-yellow-800;
  }
  
  .badge-info {
    @apply badge bg-blue-100 text-blue-800;
  }
  
  .badge-danger {
    @apply badge bg-red-100 text-red-800;
  }
  
  /* Navigation */
  .nav-link {
    @apply px-3 py-2 rounded-md text-sm font-medium;
    @apply transition-all duration-200 ease-in-out;
    @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }
  
  .nav-link.active {
    @apply text-primary-600 border-b-2 border-primary-600;
  }
  
  .nav-link:not(.active) {
    @apply text-gray-500 hover:text-primary-600 border-b-2 border-transparent;
  }
  
  /* Modal System */
  .modal-backdrop {
    @apply fixed inset-0 bg-black bg-opacity-50 z-50;
    @apply flex items-center justify-center p-4;
    @apply transition-all duration-300 ease-in-out;
  }
  
  .modal {
    @apply bg-white rounded-xl max-w-md w-full;
    @apply transform transition-all duration-300 ease-in-out;
    @apply shadow-2xl;
  }
  
  /* Loading States */
  .skeleton {
    @apply bg-gray-200 rounded animate-pulse;
  }
  
  .spinner {
    @apply inline-block w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin;
  }
  
  /* Advanced Search Styles */
  .search-suggestions {
    @apply absolute top-full left-0 right-0 mt-1 bg-white rounded-md shadow-lg border border-gray-200 py-2 z-50 max-h-80 overflow-y-auto;
    animation: slideDown 0.2s ease-out;
  }
  
  .search-suggestion-item {
    @apply block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors;
  }
  
  .search-suggestion-item:hover {
    @apply bg-blue-50 text-blue-700;
  }
  
  /* Dropdown Animations */
  .dropdown-enter {
    animation: dropdownSlideIn 0.2s ease-out;
  }
  
  .dropdown-exit {
    animation: dropdownSlideOut 0.2s ease-in;
  }
  
  /* Enhanced Mobile Menu */
  .mobile-menu-overlay {
    @apply fixed inset-0 z-[60] bg-black bg-opacity-50 transition-opacity duration-300;
  }
  
  .mobile-menu-panel {
    @apply fixed inset-y-0 left-0 w-72 bg-white shadow-xl z-[70] flex flex-col transform transition-transform duration-300;
  }
  
  .animate-slide-in-left {
    animation: slideInLeft 0.3s ease-out;
  }
  
  .animate-slide-out-left {
    animation: slideOutLeft 0.3s ease-in;
  }
  
  /* Notification Styles */
  .notification-item {
    @apply px-4 py-3 hover:bg-gray-50 transition-colors;
  }
  
  .notification-item.unread {
    @apply border-l-4 border-blue-500 bg-blue-50;
  }
  
  .notification-item.unread:hover {
    @apply bg-blue-100;
  }
  
  /* Message Styles */
  .message-item {
    @apply px-4 py-3 hover:bg-gray-50 transition-colors;
  }
  
  .message-item.unread {
    @apply border-l-4 border-green-500 bg-green-50;
  }
  
  .message-item.unread:hover {
    @apply bg-green-100;
  }
  
  /* User Dropdown Enhancements */
  .user-dropdown {
    @apply absolute right-0 mt-2 w-72 bg-white rounded-md shadow-lg border border-gray-200 py-2 z-50;
    animation: dropdownSlideIn 0.2s ease-out;
  }
  
  .user-stats-grid {
    @apply grid grid-cols-3 gap-4 mt-3 pt-3 border-t border-gray-100;
  }
  
  .user-stat-item {
    @apply text-center;
  }
  
  .user-nav-item {
    @apply flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors;
  }
  
  .user-nav-item:hover {
    @apply bg-blue-50 text-blue-700;
  }
  
  /* Category Dropdown */
  .category-dropdown {
    @apply absolute top-full left-0 mt-1 w-56 bg-white rounded-md shadow-lg border border-gray-200 py-2 z-50;
    animation: dropdownSlideIn 0.2s ease-out;
  }
  
  .category-item {
    @apply block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors;
  }
  
  .category-item:hover {
    @apply bg-blue-50 text-blue-700;
  }
}

/* ==========================================================================
   UTILITY CLASSES - Custom Utilities
   ========================================================================== */

@layer utilities {
  
  /* Text Gradients */
  .text-gradient {
    background: linear-gradient(to right, var(--color-primary-600), var(--color-primary-800));
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
  }
  
  /* Background Gradients */
  .bg-gradient-primary {
    background: linear-gradient(to right, var(--color-primary-600), var(--color-primary-800));
  }
  
  /* Custom Shadows */
  .shadow-soft {
    box-shadow: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);
  }
  
  .shadow-glow {
    box-shadow: 0 0 0 1px rgba(59, 130, 246, 0.1), 0 4px 16px rgba(59, 130, 246, 0.12);
  }
  
  /* Interactive States */
  .scale-98 {
    transform: scale(0.98);
  }
  
  .scale-102 {
    transform: scale(1.02);
  }
  
  /* Glass Effect */
  .glass {
    @apply bg-white bg-opacity-20 backdrop-blur-lg border border-white border-opacity-20;
  }
  
  /* Scrollbar Hide */
  .scrollbar-hide {
    -ms-overflow-style: none;  /* Internet Explorer 10+ */
    scrollbar-width: none;  /* Firefox */
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;  /* Safari and Chrome */
  }
  
  /* Mobile Category Scroll */
  .category-scroll {
    scroll-behavior: smooth;
    scroll-snap-type: x mandatory;
  }
  
  .category-scroll > * {
    scroll-snap-align: start;
  }

  /* Responsive Utilities */
  .container-fluid {
    @apply w-full max-w-none px-4 sm:px-6 lg:px-8;
  }
  
  .container-sm {
    @apply max-w-screen-sm mx-auto px-4;
  }
  
  .container-md {
    @apply max-w-screen-md mx-auto px-4;
  }
  
  .container-lg {
    @apply max-w-screen-lg mx-auto px-4;
  }
  
  .container-xl {
    @apply max-w-screen-xl mx-auto px-4;
  }
  
  .container-2xl {
    @apply max-w-screen-2xl mx-auto px-4;
  }
}

/* ==========================================================================
   RESPONSIVE BREAKPOINTS - Mobile First Approach
   ========================================================================== */

/* Extra Small devices (phones, less than 576px) */
@media (max-width: 575.98px) {
  .mobile-only {
    @apply block;
  }
  
  .mobile-hidden {
    @apply hidden;
  }
  
  /* Mobile specific adjustments */
  .btn-primary,
  .btn-secondary,
  .btn-outline {
    @apply w-full sm:w-auto;
  }
  
  .card {
    @apply p-4;
  }
  
  h1 {
    @apply text-2xl;
  }
  
  h2 {
    @apply text-xl;
  }
}

/* Small devices (landscape phones, 576px and up) */
@media (min-width: 576px) and (max-width: 767.98px) {
  .sm-only {
    @apply block;
  }
}

/* Medium devices (tablets, 768px and up) */
@media (min-width: 768px) and (max-width: 991.98px) {
  .md-only {
    @apply block;
  }
  
  .mobile-only {
    @apply hidden;
  }
}

/* Large devices (desktops, 992px and up) */
@media (min-width: 992px) {
  .desktop-only {
    @apply block;
  }
  
  .mobile-only,
  .mobile-hidden {
    @apply hidden;
  }
}

/* ==========================================================================
   ACCESSIBILITY & PERFORMANCE
   ========================================================================== */

/* Reduced motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .btn-primary {
    @apply border-2 border-white;
  }
  
  .card {
    @apply border-2 border-gray-400;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-only {
    display: block !important;
  }
  
  /* Optimize for print */
  body {
    @apply text-black bg-white;
  }
  
  .card {
    @apply shadow-none border border-gray-300;
  }
}

/* ==========================================================================
   ANIMATIONS & KEYFRAMES
   ========================================================================== */

@keyframes dropdownSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes dropdownSlideOut {
  from {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  to {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
}

@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes slideOutLeft {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(-100%);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }
  33% {
    transform: translate(30px, -50px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

@keyframes gradient-shift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes gentle-bounce {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes float-delayed {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-15px);
  }
}

/* Animation Classes */
.animate-blob {
  animation: blob 7s infinite;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-float-delayed {
  animation: float-delayed 6s ease-in-out infinite 2s;
}

.animate-gradient {
  background-size: 200% 200%;
  animation: gradient-shift 3s ease infinite;
}

.animate-gentle-bounce {
  animation: gentle-bounce 3s ease-in-out infinite;
}

/* Hero Section Specific Styles */
.hero-gradient-text {
  background: linear-gradient(135deg, #8b5cf6, #ec4899, #f97316);
  background-size: 200% 200%;
  animation: gradient-shift 3s ease infinite;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  color: transparent;
}

.hero-card {
  backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.hero-button-primary {
  background: linear-gradient(135deg, #8b5cf6, #ec4899);
  background-size: 200% 200%;
  animation: gradient-shift 3s ease infinite;
  transition: all 0.3s ease;
}

.hero-button-primary:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: 0 20px 40px rgba(139, 92, 246, 0.3);
}

/* ==========================================================================
   DARK MODE SUPPORT (Optional - Ready for Implementation)
   ========================================================================== */

@media (prefers-color-scheme: dark) {
  .dark-mode-auto {
    /* Dark mode styles can be added here when needed */
  }
}