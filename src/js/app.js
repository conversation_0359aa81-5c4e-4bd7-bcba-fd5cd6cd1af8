// UGC Marketplace JavaScript

// Mobile Menu Toggle
function toggleMobileMenu() {
    const mobileMenu = document.getElementById('mobile-menu');
    const menuButton = document.getElementById('mobile-menu-button');
    
    if (mobileMenu.classList.contains('hidden')) {
        mobileMenu.classList.remove('hidden');
        document.body.style.overflow = 'hidden';
        menuButton.setAttribute('aria-expanded', 'true');
    } else {
        mobileMenu.classList.add('hidden');
        document.body.style.overflow = '';
        menuButton.setAttribute('aria-expanded', 'false');
    }
}

// Modal Functions
function openModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.remove('hidden');
        document.body.classList.add('overflow-hidden');
    }
}

function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.add('hidden');
        document.body.classList.remove('overflow-hidden');
    }
}

// Close modal when clicking outside
function setupModalCloseOnOutsideClick() {
    document.addEventListener('click', function(event) {
        if (event.target.classList.contains('modal-backdrop')) {
            const modal = event.target;
            modal.classList.add('hidden');
            document.body.classList.remove('overflow-hidden');
        }
    });
}

// Dropdown Toggle
function toggleDropdown(dropdownId) {
    const dropdown = document.getElementById(dropdownId);
    if (dropdown) {
        dropdown.classList.toggle('hidden');
    }
}

// Close dropdowns when clicking outside
function setupDropdownCloseOnOutsideClick() {
    document.addEventListener('click', function(event) {
        const dropdowns = document.querySelectorAll('[id$="-dropdown"]');
        dropdowns.forEach(dropdown => {
            if (!dropdown.contains(event.target) && !event.target.closest('[onclick*="toggleDropdown"]')) {
                dropdown.classList.add('hidden');
            }
        });
    });
}

// Tab Switching
function switchTab(tabName, activeButton) {
    // Hide all tab contents
    const tabContents = document.querySelectorAll('[id$="-tab"]');
    tabContents.forEach(tab => {
        tab.classList.add('hidden');
    });
    
    // Remove active class from all tab buttons
    const tabButtons = document.querySelectorAll('.tab-button');
    tabButtons.forEach(button => {
        button.classList.remove('border-primary-500', 'text-primary-600');
        button.classList.add('border-transparent', 'text-gray-500');
    });
    
    // Show selected tab content
    const selectedTab = document.getElementById(tabName + '-tab');
    if (selectedTab) {
        selectedTab.classList.remove('hidden');
    }
    
    // Add active class to clicked button
    if (activeButton) {
        activeButton.classList.remove('border-transparent', 'text-gray-500');
        activeButton.classList.add('border-primary-500', 'text-primary-600');
    }
}

// Form Validation
function validateForm(formId) {
    const form = document.getElementById(formId);
    if (!form) return false;
    
    const requiredFields = form.querySelectorAll('[required]');
    let isValid = true;
    
    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            field.classList.add('border-red-500');
            isValid = false;
        } else {
            field.classList.remove('border-red-500');
        }
    });
    
    return isValid;
}

// Search Functionality
function handleSearch(searchInput, resultsContainer) {
    const query = searchInput.value.toLowerCase();
    const items = resultsContainer.querySelectorAll('.search-item');
    
    items.forEach(item => {
        const text = item.textContent.toLowerCase();
        if (text.includes(query)) {
            item.style.display = 'block';
        } else {
            item.style.display = 'none';
        }
    });
}

// Filter Functions
function applyFilters() {
    const filterForm = document.getElementById('filter-form');
    if (!filterForm) return;
    
    const formData = new FormData(filterForm);
    const filters = {};
    
    for (let [key, value] of formData.entries()) {
        if (value) {
            filters[key] = value;
        }
    }
    
    // Apply filters to items
    const items = document.querySelectorAll('.filterable-item');
    items.forEach(item => {
        let shouldShow = true;
        
        Object.keys(filters).forEach(filterKey => {
            const itemValue = item.dataset[filterKey];
            if (itemValue && itemValue !== filters[filterKey]) {
                shouldShow = false;
            }
        });
        
        item.style.display = shouldShow ? 'block' : 'none';
    });
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    setupModalCloseOnOutsideClick();
    setupDropdownCloseOnOutsideClick();
    
    // Initialize any default active tabs
    const defaultTab = document.querySelector('.tab-button.active');
    if (defaultTab) {
        const tabName = defaultTab.dataset.tab;
        switchTab(tabName, defaultTab);
    }
    
    // Setup search suggestions hide on outside click
    document.addEventListener('click', hideSearchSuggestions);
});

// Utility Functions
function formatPrice(price) {
    return new Intl.NumberFormat('tr-TR', {
        style: 'currency',
        currency: 'TRY'
    }).format(price);
}

function formatDate(date) {
    return new Intl.DateTimeFormat('tr-TR', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    }).format(new Date(date));
}

// Advanced Search Functions
function showSearchSuggestions(query) {
    const suggestionsContainer = document.getElementById('search-suggestions');
    if (!suggestionsContainer) return;
    
    if (query.length > 0) {
        suggestionsContainer.classList.remove('hidden');
    } else {
        suggestionsContainer.classList.add('hidden');
    }
}

// Hide search suggestions when clicking outside
function hideSearchSuggestions(event) {
    const searchContainer = event.target.closest('.relative');
    const suggestionsContainer = document.getElementById('search-suggestions');
    
    if (!searchContainer && suggestionsContainer) {
        suggestionsContainer.classList.add('hidden');
    }
}

// Logout function
function logout() {
    if (confirm('Çıkış yapmak istediğinizden emin misiniz?')) {
        // Logout logic here
        console.log('Logging out...');
        // Redirect to login page or home
        // window.location.href = '/login';
    }
}

// Advanced Search Functions
function selectCategory(categoryId, categoryName, iconClass, iconColor) {
    document.getElementById('selected-category').textContent = categoryName;
    document.getElementById('category-dropdown').classList.add('hidden');
    
    // Update icon
    const iconElement = document.getElementById('selected-category-icon');
    if (iconElement && iconClass) {
        iconElement.className = `${iconClass} mr-2 ${iconColor}`;
    }
    
    // Animate chevron
    const chevron = document.getElementById('category-chevron');
    if (chevron) {
        chevron.style.transform = 'rotate(0deg)';
    }
    
    // Update search input placeholder based on category
    const searchInput = document.getElementById('main-search-input');
    if (categoryId === 'all') {
        searchInput.placeholder = 'İçerik üreticisi, hizmet veya anahtar kelime ara...';
    } else {
        searchInput.placeholder = `${categoryName} kategorisinde ara...`;
    }
}

function handleSearchInput(query) {
    const suggestionsContainer = document.getElementById('search-suggestions');
    if (!suggestionsContainer) return;
    
    if (query.length > 0) {
        suggestionsContainer.classList.remove('hidden');
        // Here you would typically make an API call to get search suggestions
        updateSearchSuggestions(query);
    } else {
        suggestionsContainer.classList.add('hidden');
    }
}

function updateSearchSuggestions(query) {
    // This would typically fetch suggestions from an API
    // For now, we'll show static suggestions
    const suggestionsContainer = document.getElementById('search-suggestions');
    if (!suggestionsContainer) return;
    
    // You can implement dynamic suggestion filtering here
    console.log('Searching for:', query);
}

function performSearch() {
    const searchInput = document.getElementById('main-search-input');
    const selectedCategory = document.getElementById('selected-category').textContent;
    const query = searchInput.value.trim();
    
    if (query) {
        console.log('Performing search:', {
            query: query,
            category: selectedCategory
        });
        
        // Hide suggestions
        document.getElementById('search-suggestions').classList.add('hidden');
        
        // Here you would typically redirect to search results page
        // window.location.href = `/search?q=${encodeURIComponent(query)}&category=${selectedCategory}`;
    }
}

// Enhanced dropdown management
function setupAdvancedDropdowns() {
    // Close all dropdowns when clicking outside
    document.addEventListener('click', function(event) {
        const dropdowns = [
            'category-dropdown',
            'notifications-dropdown', 
            'messages-dropdown',
            'user-dropdown',
            'search-suggestions',
            'mobile-category-dropdown',
            'mobile-search-suggestions'
        ];
        
        dropdowns.forEach(dropdownId => {
            const dropdown = document.getElementById(dropdownId);
            const trigger = event.target.closest(`[onclick*="${dropdownId}"]`);
            
            if (dropdown && !dropdown.contains(event.target) && !trigger) {
                closeDropdownWithAnimation(dropdown);
            }
        });
    });
    
    // Handle escape key to close dropdowns
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
            const dropdowns = document.querySelectorAll('[id$="-dropdown"], #search-suggestions, #mobile-search-suggestions');
            dropdowns.forEach(dropdown => {
                closeDropdownWithAnimation(dropdown);
            });
            
            // Reset chevron rotation
            const chevron = document.getElementById('category-chevron');
            if (chevron) {
                chevron.style.transform = 'rotate(0deg)';
            }
        }
    });
}

// Enhanced dropdown toggle with animations
function toggleDropdownEnhanced(dropdownId) {
    const dropdown = document.getElementById(dropdownId);
    if (!dropdown) return;
    
    const isHidden = dropdown.classList.contains('hidden');
    
    // Close all other dropdowns first
    const allDropdowns = document.querySelectorAll('[id$="-dropdown"], #search-suggestions, #mobile-search-suggestions');
    allDropdowns.forEach(otherDropdown => {
        if (otherDropdown.id !== dropdownId) {
            closeDropdownWithAnimation(otherDropdown);
        }
    });
    
    if (isHidden) {
        openDropdownWithAnimation(dropdown);
        
        // Rotate chevron for category dropdown
        if (dropdownId === 'category-dropdown') {
            const chevron = document.getElementById('category-chevron');
            if (chevron) {
                chevron.style.transform = 'rotate(180deg)';
            }
        }
    } else {
        closeDropdownWithAnimation(dropdown);
        
        // Reset chevron rotation
        if (dropdownId === 'category-dropdown') {
            const chevron = document.getElementById('category-chevron');
            if (chevron) {
                chevron.style.transform = 'rotate(0deg)';
            }
        }
    }
}

function openDropdownWithAnimation(dropdown) {
    dropdown.classList.remove('hidden');
    dropdown.style.opacity = '0';
    dropdown.style.transform = 'translateY(-10px) scale(0.95)';
    
    // Force reflow
    dropdown.offsetHeight;
    
    dropdown.style.transition = 'all 0.2s ease-out';
    dropdown.style.opacity = '1';
    dropdown.style.transform = 'translateY(0) scale(1)';
}

function closeDropdownWithAnimation(dropdown) {
    if (dropdown.classList.contains('hidden')) return;
    
    dropdown.style.transition = 'all 0.15s ease-in';
    dropdown.style.opacity = '0';
    dropdown.style.transform = 'translateY(-10px) scale(0.95)';
    
    setTimeout(() => {
        dropdown.classList.add('hidden');
        dropdown.style.transition = '';
        dropdown.style.opacity = '';
        dropdown.style.transform = '';
    }, 150);
}

// Search functionality enhancements
function setupSearchEnhancements() {
    const searchInput = document.getElementById('main-search-input');
    if (searchInput) {
        // Handle Enter key for search
        searchInput.addEventListener('keydown', function(event) {
            if (event.key === 'Enter') {
                event.preventDefault();
                performSearch();
            }
        });
        
        // Handle search suggestions navigation with arrow keys
        searchInput.addEventListener('keydown', function(event) {
            const suggestions = document.getElementById('search-suggestions');
            if (!suggestions || suggestions.classList.contains('hidden')) return;
            
            const suggestionItems = suggestions.querySelectorAll('a');
            let currentIndex = -1;
            
            // Find currently highlighted item
            suggestionItems.forEach((item, index) => {
                if (item.classList.contains('bg-gray-100')) {
                    currentIndex = index;
                }
            });
            
            if (event.key === 'ArrowDown') {
                event.preventDefault();
                currentIndex = Math.min(currentIndex + 1, suggestionItems.length - 1);
            } else if (event.key === 'ArrowUp') {
                event.preventDefault();
                currentIndex = Math.max(currentIndex - 1, -1);
            }
            
            // Update highlighting
            suggestionItems.forEach((item, index) => {
                if (index === currentIndex) {
                    item.classList.add('bg-gray-100');
                } else {
                    item.classList.remove('bg-gray-100');
                }
            });
        });
    }
}

// Notification management
function markAllNotificationsRead() {
    // This would typically make an API call
    console.log('Marking all notifications as read');
    
    // Update UI
    const notificationBadge = document.querySelector('.bg-red-500');
    if (notificationBadge) {
        notificationBadge.style.display = 'none';
    }
    
    // Remove unread styling from notifications
    const unreadNotifications = document.querySelectorAll('.border-blue-500, .bg-blue-50');
    unreadNotifications.forEach(notification => {
        notification.classList.remove('border-blue-500', 'bg-blue-50');
    });
}

// Enhanced mobile menu with better animations
function toggleMobileMenuEnhanced() {
    const mobileMenu = document.getElementById('mobile-menu');
    const menuButton = document.getElementById('mobile-menu-button');
    const body = document.body;
    
    if (mobileMenu.classList.contains('hidden')) {
        mobileMenu.classList.remove('hidden');
        body.classList.add('overflow-hidden');
        menuButton.setAttribute('aria-expanded', 'true');
        
        // Add animation class
        setTimeout(() => {
            const menuContent = mobileMenu.querySelector('.fixed');
            if (menuContent) {
                menuContent.classList.add('animate-slide-in-left');
            }
        }, 10);
    } else {
        const menuContent = mobileMenu.querySelector('.fixed');
        if (menuContent) {
            menuContent.classList.add('animate-slide-out-left');
            setTimeout(() => {
                mobileMenu.classList.add('hidden');
                body.classList.remove('overflow-hidden');
                menuContent.classList.remove('animate-slide-in-left', 'animate-slide-out-left');
            }, 300);
        } else {
            mobileMenu.classList.add('hidden');
            body.classList.remove('overflow-hidden');
        }
        menuButton.setAttribute('aria-expanded', 'false');
    }
}

// Initialize enhanced features
document.addEventListener('DOMContentLoaded', function() {
    setupModalCloseOnOutsideClick();
    setupDropdownCloseOnOutsideClick();
    setupAdvancedDropdowns();
    setupSearchEnhancements();
    
    // Initialize any default active tabs
    const defaultTab = document.querySelector('.tab-button.active');
    if (defaultTab) {
        const tabName = defaultTab.dataset.tab;
        switchTab(tabName, defaultTab);
    }
});

// Export functions for global use
window.toggleMobileMenu = toggleMobileMenuEnhanced;
window.openModal = openModal;
window.closeModal = closeModal;
window.toggleDropdown = toggleDropdown;
window.switchTab = switchTab;
window.validateForm = validateForm;
window.handleSearch = handleSearch;
window.applyFilters = applyFilters;
window.formatPrice = formatPrice;
window.formatDate = formatDate;
window.showSearchSuggestions = showSearchSuggestions;
window.logout = logout;
window.selectCategory = selectCategory;
window.handleSearchInput = handleSearchInput;
window.performSearch = performSearch;
window.markAllNotificationsRead = markAllNotificationsRead;
window.selectMobileCategory = selectMobileCategory;
window.handleMobileSearchInput = handleMobileSearchInput;
window.showMobileSearchSuggestions = showMobileSearchSuggestions;
window.performMobileSearch = performMobileSearch;
window.toggleDropdownEnhanced = toggleDropdownEnhanced;

// Mobile search functions
function selectMobileCategory(categoryId, categoryName) {
    document.getElementById('mobile-category-dropdown').classList.add('hidden');
    
    // Update mobile search input placeholder based on category
    const mobileSearchInput = document.getElementById('mobile-search-input');
    if (categoryId === 'all') {
        mobileSearchInput.placeholder = 'Hangi hizmeti arıyorsunuz?';
    } else {
        mobileSearchInput.placeholder = `${categoryName} ara...`;
    }
}

function handleMobileSearchInput(query) {
    const suggestionsContainer = document.getElementById('mobile-search-suggestions');
    if (!suggestionsContainer) return;
    
    if (query.length > 0) {
        suggestionsContainer.classList.remove('hidden');
    } else {
        suggestionsContainer.classList.add('hidden');
    }
}

function showMobileSearchSuggestions() {
    const suggestionsContainer = document.getElementById('mobile-search-suggestions');
    const searchInput = document.getElementById('mobile-search-input');
    
    if (suggestionsContainer && searchInput.value.length > 0) {
        suggestionsContainer.classList.remove('hidden');
    }
}

function performMobileSearch() {
    const searchInput = document.getElementById('mobile-search-input');
    const query = searchInput.value.trim();
    
    if (query) {
        console.log('Performing mobile search:', query);
        
        // Hide suggestions
        document.getElementById('mobile-search-suggestions').classList.add('hidden');
        
        // Here you would typically redirect to search results page
        // window.location.href = `/search?q=${encodeURIComponent(query)}`;
    }
}